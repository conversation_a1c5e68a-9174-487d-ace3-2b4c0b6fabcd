<script setup lang="ts">
const authStore = useAuthStore()
const { isNotVerifyAccount, canResendActivationEmailAfter, user }
  = storeToRefs(authStore)

const resendActivationEmailInterval = ref(null as any)

const appStore = useAppStore()
const { adsBanner } = storeToRefs(appStore)

// Refs for scrolling text animation
const titleRef = ref<HTMLElement>()
const titleContainerRef = ref<HTMLElement>()
const shouldScroll = ref(false)

const resendActivationEmail = async () => {
  authStore.canResendActivationEmailAfter = 30
  resendActivationEmailInterval.value = setInterval(() => {
    authStore.canResendActivationEmailAfter--
    if (authStore.canResendActivationEmailAfter === 0) {
      clearInterval(resendActivationEmailInterval.value)
    }
  }, 1000)
  authStore.resendVerificationEmail()
}

// Check if title needs scrolling
const checkTitleOverflow = () => {
  if (titleRef.value && titleContainerRef.value) {
    const titleWidth = titleRef.value.scrollWidth
    const containerWidth = titleContainerRef.value.clientWidth
    shouldScroll.value = titleWidth > containerWidth
  }
}

onMounted(() => {
  if (authStore.canResendActivationEmailAfter > 0) {
    resendActivationEmailInterval.value = setInterval(() => {
      authStore.canResendActivationEmailAfter--
      if (authStore.canResendActivationEmailAfter === 0) {
        clearInterval(resendActivationEmailInterval.value)
      }
    }, 1000)
  }

  // Check title overflow after mount and on resize
  nextTick(() => {
    checkTitleOverflow()
  })

  window.addEventListener('resize', checkTitleOverflow)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkTitleOverflow)
})

// Watch for adsBanner changes to recheck overflow
watch(adsBanner, () => {
  nextTick(() => {
    checkTitleOverflow()
  })
}, { deep: true })
</script>

<template>
  <div class="fixed top-0 left-0 right-0 z-50">
    <UBanner
      v-if="isNotVerifyAccount"
      color="warning"
      :title="$t('auth.notVerifyAccount')"
      :actions="[
        {
          label:
            canResendActivationEmailAfter > 0
              ? `Resend in ${canResendActivationEmailAfter} seconds`
              : $t('auth.resendActivationEmail'),
          onClick: resendActivationEmail,
          disabled: canResendActivationEmailAfter > 0
        }
      ]"
      icon="i-lucide-info"
    />
    <div
      v-else-if="adsBanner"
      class="bg-warning-50 dark:bg-warning-950 border-warning-200 dark:border-warning-800 border-b"
    >
      <div class="flex items-center gap-3 px-4 py-3">
        <!-- Icon -->
        <UIcon
          v-if="adsBanner.icon"
          :name="adsBanner.icon"
          class="text-warning-500 dark:text-warning-400 flex-shrink-0"
          size="20"
        />

        <!-- Scrolling Title Container -->
        <div
          ref="titleContainerRef"
          class="flex-1 overflow-hidden relative"
        >
          <div
            ref="titleRef"
            :class="[
              'text-warning-900 dark:text-warning-100 font-medium whitespace-nowrap',
              shouldScroll ? 'scrolling-text' : ''
            ]"
          >
            {{ $t(adsBanner.title) }}
          </div>
        </div>

        <!-- Action Button -->
        <UButton
          :label="$t('Check now')"
          trailing-icon="i-lucide-arrow-right"
          color="warning"
          variant="soft"
          size="sm"
          @click="navigateTo('/pricing')"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.scrolling-text {
  animation: scroll-horizontal 15s linear infinite;
  animation-delay: 2s;
}

.scrolling-text:hover {
  animation-play-state: paused;
}

@keyframes scroll-horizontal {
  0% {
    transform: translateX(100%);
  }
  10% {
    transform: translateX(100%);
  }
  90% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* LED-like glow effect */
.scrolling-text {
  text-shadow:
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor;
  filter: brightness(1.1);
}

/* Smooth transition when scrolling starts/stops */
.scrolling-text {
  transition: transform 0.3s ease-in-out;
}

/* Alternative smoother animation for better readability */
@media (prefers-reduced-motion: no-preference) {
  .scrolling-text {
    animation: scroll-smooth 20s linear infinite;
    animation-delay: 3s;
  }

  @keyframes scroll-smooth {
    0% {
      transform: translateX(100%);
    }
    5% {
      transform: translateX(100%);
    }
    95% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(-100%);
    }
  }
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  .scrolling-text {
    animation: none;
    transform: none;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
</style>
