<script setup lang="ts">
const { t } = useI18n()
const productStore = useProductStore()
const { getServicePriceByModelName } = storeToRefs(productStore)

useSeoMeta({
  title: `${t('pricing.title')} - Imagen`,
  description: t('pricing.description'),
  ogTitle: `${t('pricing.title')} - Imagen`,
  ogDescription: t('pricing.description')
})

const plans = computed(() => {
  return [
    {
      title: t('Imagen'),
      description: t('Create images from text prompts.'),
      price:
        '~' + getServicePriceByModelName.value('imagen-flash')?.effective_price,
      billingCycle: t('/Image'),
      billingPeriod: 'Credits',
      features: [
        t('Gemini 2.0 Flash')
        + ` (${getServicePriceByModelName.value('imagen-flash')?.effective_price} Credits)`,
        t('Imagen 4 Fast')
        + ` (${getServicePriceByModelName.value('imagen-4-fast')?.effective_price} Credits)`,
        t('Imagen 4')
        + ` (${getServicePriceByModelName.value('imagen-4')?.effective_price} Credits)`,
        t('Imagen 4 Ultra')
        + ` (${getServicePriceByModelName.value('imagen-4-ultra')?.effective_price} Credits)`,
        t('{n}+ Styles', { n: 16 }),
        t('Image Reference')
      ],
      button: {
        label: t('Buy credits'),
        variant: 'soft' as const,
        color: 'neutral'
      }
    },
    {
      title: t('Video Gen'),
      scale: true,
      description: t('Generate videos from text prompts and images.'),
      price: '~' + getServicePriceByModelName.value('veo-2')?.effective_price,
      billingCycle: t('/Video'),
      billingPeriod: 'Credits',
      features: [
        t('Veo 2')
        + ` (${getServicePriceByModelName.value('veo-2')?.effective_price} Credits)`,
        t('Veo 3')
        + ` (${getServicePriceByModelName.value('veo-3')?.effective_price} Credits)`,
        t('Text to Video'),
        t('Image to Video'),
        t('Up to 8 seconds'),
        t('1080p Quality'),
        t('Multiple Styles')
      ],
      button: {
        label: t('Buy credits'),
        variant: 'soft' as const,
        color: 'neutral'
      }
    },
    {
      title: t('Speech Gen'),
      description: t('Convert text and documents to natural speech.'),
      price:
        '~' + getServicePriceByModelName.value('tts-flash')?.effective_price,
      billingCycle: t('/1 character'),
      billingPeriod: 'Credits',
      features: [
        t('Text to Speech'),
        t('Document to Speech'),
        t('Multi-Speaker Support'),
        t('400+ Voices'),
        t('Multiple Languages'),
        t('Emotion Control')
      ],
      button: {
        label: t('Buy credits'),
        variant: 'soft' as const,
        color: 'neutral'
      }
    },
    {
      title: t('Dialogue Gen'),
      description: t('Create natural conversations with multiple speakers.'),
      price:
        '~' + getServicePriceByModelName.value('tts-flash')?.effective_price,
      billingCycle: t('/1 character'),
      billingPeriod: 'Credits',
      features: [
        t('Multi-Speaker Dialogue'),
        t('Natural Conversations'),
        t('Voice Customization'),
        t('Emotion Expression'),
        t('Script Generation'),
        t('Audio Export')
      ],
      button: {
        label: t('Buy credits'),
        variant: 'soft' as const,
        color: 'neutral'
      }
    }
  ]
})
</script>

<template>
  <UPage>
    <UContainer>
      <UPageHeader
        :title="$t('pricing.title')"
        :description="$t('Simple and flexible. Only pay for what you use.')"
      />
    </UContainer>
    <UPageSection>
      <UPricingPlans
        compact
        :plans="plans"
      />
    </UPageSection>
  </UPage>
</template>
