<script setup lang="ts">
definePageMeta({
  middleware: 'auth'
})

const authStore = useAuthStore()
const { user_credit } = storeToRefs(authStore)

onMounted(() => {
  authStore.userMe()
})

const props = defineProps<{
  period: any
  range: any
}>()

const creditStats = computed(() => {
  return [
    {
      title: 'Available Credits',
      icon: 'mingcute:wallet-2-fill',
      value: user_credit.value?.available_credit || 0
    },
    {
      title: 'Purchased Credits',
      icon: 'streamline-freehand:e-commerce-click-buy',
      value: user_credit.value?.purchased_credit || 0
    },
    {
      title: 'Plan Credits',
      icon: 'tabler:gift',
      value: user_credit.value?.plan_credit || 0
    },
    {
      title: 'Locked Credits',
      icon: 'tdesign:rotate-locked-filled',
      value: user_credit.value?.locked_credit || 0
    }
  ]
})

const { data: stats } = await useAsyncData<any[]>(
  'stats',
  async () => {
    return baseStats.map((stat) => {
      const value = 1
      const variation = 2

      return {
        title: stat.title,
        icon: stat.icon,
        value: stat.formatter ? stat.formatter(value) : value,
        variation
      }
    })
  },
  {
    watch: [() => props.period, () => props.range],
    default: () => []
  }
)
</script>

<template>
  <UPage>
    <div>
      <div class="mb-4">
        <h2 class="text-xl font-bold">
          {{ $t("Credit Statistics") }}
        </h2>
        <p class="text-sm text-muted">
          {{ $t("Overview of your credits status.") }}
        </p>
      </div>
      <UPageGrid class="lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-px">
        <UPageCard
          v-for="(stat, index) in creditStats"
          :key="index"
          :icon="stat.icon"
          :title="$t(stat.title)"
          variant="subtle"
          :ui="{
            container: 'gap-y-1.5',
            wrapper: 'items-center',
            leading:
              'p-2.5 rounded-full bg-primary/10 ring ring-inset ring-primary/25 flex-col',
            title: 'font-normal text-muted text-xs uppercase'
          }"
          class="lg:rounded-none first:rounded-l-lg last:rounded-r-lg hover:z-1"
        >
          <div class="flex items-center gap-2 mx-auto">
            <span class="text-xl font-semibold text-highlighted">
              {{ formatNumber(stat.value) }}
            </span>
          </div>
        </UPageCard>
      </UPageGrid>
    </div>

    <div class="mt-6 flex flex-col gap-4">
      <div>
        <BuyCreditsQuickTopup />
      </div>
      <div>
        <BuyCreditsCustomTopup />
      </div>
    </div>
    <BuyCreditsDrawer />
  </UPage>
</template>
